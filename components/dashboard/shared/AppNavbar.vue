
<template>
  <client-only>
    <div>
      <nav class="d-app_navbar">
        <div class="welcome_wrapper" v-if="userData != null">
          <h4>{{ $t('admin.hello') }} {{ userData.company_name + '👋🏻' }}</h4>
        </div>
        <!-- end::welcome_wrapper -->

        <div class="options_wrapper">
          <div class="item bordered">
            <a href="javascript:;" @click.prevent="handleCreateBid" class="btn btn-default">
              <svg class="icon">
                <use xlink:href="~/static/sprite.svg#plus"></use>
              </svg>
              <span> {{ $t('admin.create_bid') }}</span>
            </a>
          </div>
          <!-- end::item -->
          <div class="item">
            <b-dropdown class="lang_switcher" no-caret>
              <template #button-content>
                <img :src="require(`~/static/${$i18n.locale}.svg`)" alt="flag" />
                <span v-if="$i18n.locale == 'en'">En</span>
                <span v-if="$i18n.locale == 'ar'">Ar</span>
                <svg class="icon">
                  <use xlink:href="~/static/sprite.svg#angle-down"></use>
                </svg>
              </template>
              <b-dropdown-item @click="switchMyLang('en')">
                English
              </b-dropdown-item>
              <b-dropdown-item @click="switchMyLang('ar')">
                العربية
              </b-dropdown-item>
            </b-dropdown>
          </div>
          <!-- end::item -->
          <div class="item">
            <button type="button" class="btn btn-default notification" v-b-toggle.notifications>
              <svg class="icon" @click="refetchNotification">
                <use xlink:href="~/static/sprite.svg#notifications"></use>
              </svg>
              <span class="badge">
                {{ unread }}
              </span>
            </button>
          </div>
          <!-- end::item -->
          <div class="item">
            <button type="button" class="btn btn-default avatar" v-b-toggle.client_info v-if="userData != null">
              <img :src="userData.logo" alt="avatar" />
            </button>
          </div>
          <!-- end::item -->
        </div>
        <!-- end::options_wrapper -->
      </nav>

      <nav class="res_navabr_dahsboard">
        <div class="top_wrapper">
          <div class="row">
            <div class="col-6">
              <div class="logo_wrapper">
                <nuxt-link :to="localePath('/dashboard')">
                  <img src="~/static/logo.svg" alt="logo" />
                </nuxt-link>
              </div>
            </div>
            <!-- end::col -->
            <div class="col-6">
              <div class="sidebar_trigger">
                <button type="button" class="btn btn-default" v-b-toggle.sidebar>
                  <svg class="icon">
                    <use xlink:href="~/static/sprite.svg#menu-burg"></use>
                  </svg>
                </button>
              </div>
            </div>
            <!-- end::col -->
          </div>
          <!-- end::row -->
        </div>
        <!-- end::top_wrapper -->

        <div class="bottom_wrapper">
          <div class="row">
            <div class="col-7">
              <div class="profile_wrapper">
                <a href="javascript:;" @click.prevent="handleCreateBid" class="btn btn-default">
                  <svg class="icon">
                    <use xlink:href="~/static/sprite.svg#plus"></use>
                  </svg>
                  <span> {{ $t('admin.create_bid') }}</span>
                </a>
              </div>
            </div>
            <!-- end::col -->
            <div class="col-5">
              <div class="options_wrapper">
                <div class="item">
                  <b-dropdown class="lang_switcher" no-caret>
                    <template #button-content>
                      <img :src="require(`~/static/${$i18n.locale}.svg`)" alt="flag" />
                      <span v-if="$i18n.locale == 'en'">En</span>
                      <span v-if="$i18n.locale == 'ar'">Ar</span>
                      <svg class="icon">
                        <use xlink:href="~/static/sprite.svg#angle-down"></use>
                      </svg>
                    </template>
                    <b-dropdown-item @click="switchMyLang('en')">
                      English
                    </b-dropdown-item>
                    <b-dropdown-item @click="switchMyLang('ar')">
                      العربية
                    </b-dropdown-item>
                  </b-dropdown>
                </div>
                <!-- end::item -->
                <div class="item">
                  <button type="button" class="btn btn-default notification" v-b-toggle.notifications>
                    <svg class="icon" @click="refetchNotification">
                      <use xlink:href="~/static/sprite.svg#notifications"></use>
                    </svg>
                    <span class="badge">
                      {{ unread }}
                    </span>
                  </button>
                </div>
                <!-- end::item -->
              </div>
              <!-- end::options_wrapper -->
            </div>
            <!-- end::col -->
          </div>
          <!-- end::row -->
        </div>
        <!-- end::bottom_wrapper -->
      </nav>

      <b-sidebar id="notifications" :title="$t('admin.notifications')" shadow>
        <div class="px-3 py-3">
          <div class="header_wrapper">
            <p class="count">
              {{ unread }} {{ $t('admin.new_notifications') }}
            </p>
            <div>
              <a href="javascript:;" @click="markAllAsRead">
                {{ $t('admin.mark_as_read') }}
              </a>
              <a href="javascript:;" style="color: #cc0000" @click="confirmDelete">
                {{ $t('admin.delete_notify') }}
              </a>
            </div>
          </div>
          <!-- end::header_wrapper -->

          <div class="body_wrapper">
            <div class="wrapper" :class="{ unread: item.readed == false }"
              @click="redirectDetails(item.object_id, item.type, item.id)" v-for="(item, idx) in notifications"
              v-b-toggle.notifications :key="idx">
              <div class="icon_wrapper">
                <svg class="icon">
                  <use xlink:href="~/static/sprite.svg#act-create" v-if="
                    item.type == 'CreatedBid' ||
                    item.type == 'BidHasBeenUpdated'
                  "></use>
                  <use xlink:href="~/static/sprite.svg#act-invite" v-if="
                    item.type == 'InvitedToBid' || item.type == 'BidInvite'
                  "></use>
                  <use xlink:href="~/static/sprite.svg#act-submit" v-if="
                    item.type == 'SubmittedOffer' ||
                    item.type == 'NewOfferAdded' ||
                    item.type == 'OfferUpdated' ||
                    item.type == 'OfferUpdated'
                  "></use>
                </svg>
              </div>
              <div class="info">
                <h6>{{ item.text }}</h6>
                <p class="date">{{ $moment(item.created_at).format('ll') }}</p>
              </div>
            </div>
            <!-- end::wrapper -->
          </div>
          <!-- end::body_wrapper -->
        </div>
      </b-sidebar>
    </div>
  </client-only>
</template>

<script>
// importing vuex tools
import { mapGetters } from 'vuex'
import { mapState } from 'vuex'

export default {
  name: 'DashboardNavbar',
  mounted() {
    if (this.$store.getters['localStorage/notifications'] == null) {
      this.$axios.$get('/client/auth/notifications').then((res) => {
        this.$store.commit(
          'localStorage/SET_NOTIFICATIONS',
          res.data.notifications
        )
        this.$store.commit(
          'localStorage/SET_UNREAD_NOTIFICATION',
          res.notificationsCount
        )
      })
    }

    this.connectToSSE()
  },
  computed: {
    ...mapGetters({
      notifications: ['localStorage/get_notifications'],
      unread: ['localStorage/get_unread_notifications'],
    }),
    ...mapState({
      homepage: (state) => state.localStorage.dashboard,
      userToken: (state) => state.localStorage.userToken,
    }),
  },
  methods: {
    openNotifications() {
      this.$root.$emit('bv::toggle::sidebar', 'notifications')
      this.refetchNotification()
    },
    async refetchNotification() {
      await this.$axios.$get('/client/auth/notifications').then((res) => {
        this.$store.commit(
          'localStorage/SET_NOTIFICATIONS',
          res.data.notifications
        )
        this.$store.commit(
          'localStorage/SET_UNREAD_NOTIFICATION',
          res.notificationsCount
        )
      })
    },
    handleCreateBid() {
      if (this.homepage.insights.current_package.type != 'NONE') {
        this.$router.push(this.localePath('/dashboard/mybids/create'))
      } else {
        this.$swal({
          title: this.$t('admin.no_subscribe'),
          text: this.$t('admin.no_subscribe_desc'),
          icon: 'error',
          confirmButtonText: this.$t('admin.thanks'),
          confirmButtonColor: '#0F5296',
          showCancelButton: false,
        })
      }
    },
    switchMyLang(locale) {
      this.$store.commit('localStorage/SET_CURRENT_LOCALE', locale)
      import(`~/locales/${locale}`).then((module) => {
        this.$i18n.setLocaleMessage(locale, module.default)
        window.history.replaceState('', '', this.switchLocalePath(locale))
        this.$nuxt.$router.go()
      })
    },
    async redirectDetails(object_id, type, id) {
      await this.$axios
        .post(`/client/auth/read_notifications`, {
          notification: id,
        })
        .then((res) => {
          if (res.data.errorCode == 0) {
            this.$store.commit('localStorage/SET_NOTIFICATION_AS_READ', id)
            this.$store.commit(
              'localStorage/SET_UNREAD_NOTIFICATION',
              res.data.notificationsCount
            )
            if (object_id != null) {
              this.$router.push(
                this.localePath({
                  name: 'dashboard-bids-id',
                  params: { id: object_id },
                })
              )
            }
          }
        })
    },
    async markAllAsRead() {
      await this.$axios
        .post(`/client/auth/read_notifications`, {
          notification: null,
        })
        .then((res) => {
          if (res.data.errorCode == 0) {
            this.$store.commit('localStorage/SET_NOTIFICATIONS_AS_READ')
            this.$store.commit(
              'localStorage/SET_UNREAD_NOTIFICATION',
              res.data.notificationsCount
            )
          }
        })
    },
    confirmDelete() {
      this.$swal({
        title: this.$t('admin.delete_notify'),
        text: this.$t('admin.delete_notify_desc'),
        icon: 'success',
        confirmButtonText: this.$t('admin.cancel'),
        confirmButtonColor: '#1E805D',
        showDenyButton: true,
        denyButtonText: this.$t('admin.delete_notify'),
      }).then((result) => {
        if (result.isDenied) {
          this.deleteAll()
        }
      })
    },
    async deleteAll() {
      await this.$axios.delete('/client/auth/notification').then((res) => {
        if (res.data.errorCode == 0) {
          this.$store.commit('localStorage/EMPTY_NOTIFICATION')
          this.$store.commit(
            'localStorage/SET_UNREAD_NOTIFICATION',
            res.data.notificationsCount
          )
        }
      })
    },
    connectToSSE() {
      // Check if userToken exists
      if (!this.userToken) {
        console.warn('No user token available for SSE connection');
        return;
      }

      // Close existing connection if any
      if (this.eventSource) {
        this.eventSource.close();
        this.eventSource = null;
      }

      try {
        const token = this.userToken.replace('Bearer ', '');
        this.eventSource = new EventSource(`https://backend-api.munaqes.com/api/client/auth/notifications-sse?auth_token=${token}`);

        this.eventSource.addEventListener('unreadCount', (event) => {
          this.$store.commit('localStorage/SET_UNREAD_NOTIFICATION', event.data);
        });

        this.eventSource.onerror = (e) => {
          console.error('SSE connection error:', e);
          this.eventSource.close();
          this.eventSource = null;
          
          // Only reconnect if we still have a valid token
          if (this.userToken) {
            setTimeout(() => this.connectToSSE(), 5000);
          }
        };
      } catch (error) {
        console.error('Failed to create SSE connection:', error);
      }
    },

  },
}
</script>

<style lang="scss">
#notifications {
  background-color: #fff !important;
  width: 380px;

  .header_wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    .count {
      margin-bottom: 0;
      font-weight: 500;
      font-size: 15px;
    }

    a {
      font-size: 14px;
      display: block;
      text-align: center;
      font-weight: 500;
      color: $base-color;
    }
  }

  .body_wrapper {
    .wrapper {
      padding: 10px 15px;
      border-bottom: 1px solid #eee;
      display: flex;

      &:hover {
        cursor: pointer;

        .info {
          h6 {
            color: $base-color;
          }
        }
      }

      &:last-child {
        border-bottom: none;
      }

      &.unread {
        .info {

          h6,
          .date {
            font-weight: 600;
          }
        }
      }

      .icon_wrapper {
        width: 39px;
        height: 39px;
        border-radius: 6px;
        background-color: $base-color-opacity;
        display: flex;
        justify-content: center;
        align-items: center;

        .icon {
          width: 22px;
          height: 22px;
          stroke: $base-color;
        }
      }

      .info {
        width: calc(100% - 49px);
        margin-inline-start: 10px;

        h6 {
          margin-bottom: 8px;
          font-size: 16px;
          font-weight: 400;
          text-transform: capitalize;
        }

        .date {
          text-align: end;
          margin-bottom: 0;
          font-weight: 400;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
